from dataclasses import dataclass
from scipy.spatial.transform import Rotation as R
import numpy as np
import open3d as o3d

from camera import Helios2Camera

@dataclass
class BoxPosition:
    center: np.ndarray
    z_position: float
    extent: np.ndarray
    rotation: np.ndarray  # zxy
    volume: float


def get_box_position(pcd, visualize=False):
    # ROI box (without conveyor)
    pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 1350)))
    
    points = np.asarray(pcd.points)
    
    # Get top (low Z value) of box by taking 10th percentile of Z values
    z_values = points[:, 2]
    top_box_z = float(np.percentile(z_values, 10))
    print(f"Top of box (10th percentile Z): {top_box_z:.3f} mm")
    
    # Isolate top of box (50 mm margin around top_box_z)
    pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-np.inf, -np.inf, top_box_z - 50), max_bound=(np.inf, np.inf, top_box_z + 50)))
    points = np.asarray(pcd.points)
    print(f"Min and max Z after cropping: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm")
    
    # Remove single points (noise) but keep track of outliers
    # cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=1.0)
    cl, ind = pcd.remove_radius_outlier(nb_points=16, radius=10)
    pcd = pcd.select_by_index(ind)
    
    # Select the edges (less work for OBB in the next step, which makes it more stable)
    cl2, ind2 = pcd.remove_radius_outlier(nb_points=16, radius=10)
    pcd = pcd.select_by_index(ind2, invert=True)
    
    obb = pcd.get_minimal_oriented_bounding_box()
    print(f"OBB center: {obb.center}")
    print(f"OBB extent: {obb.extent}")
    print(f"OBB rotation matrix: {obb.R}")
    print(f"OBB volume: {obb.volume()}")
    
    # We want to have the longest axis of the OBB be the length: the first axis.
    # The second axis should be the width, and the third axis should be the height.
    # The shortest axis of the OBB is the height.
    obb_extent = obb.extent.copy()
    obb_R = obb.R.copy()
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    if obb_extent[1] < obb_extent[2]:
        obb_extent = np.array([obb_extent[0], obb_extent[2], obb_extent[1]])
        obb_R = obb_R @ np.array([[1, 0, 0], [0, 0, 1], [0, -1, 0]])
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    print(f"OBB extent after sorting: {obb_extent}")
    
    # Get rotation angles from rotation matrix
    r = R.from_matrix(obb_R)
    angles = r.as_euler('zxy', degrees=True)
    print(f"z rotation: {angles[0]:.3f} deg, x rotation: {angles[1]:.3f} deg, y rotation: {angles[2]:.3f} deg")

    if visualize:
        # Set up visualization
        vis = o3d.visualization.Visualizer()  # type: ignore
        vis.create_window(window_name=f"PLY Viewer")
        vis.add_geometry(pcd)
        vis.add_geometry(obb)

        # Set rendering options for better visualization
        render_option = vis.get_render_option()
        render_option.point_size = 2.0  # Make points more visible
        render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

        # Run the visualizer
        vis.run()
        vis.destroy_window()
        
    return BoxPosition(obb.center, top_box_z, obb_extent, angles, obb.volume())



if __name__ == "__main__":
    camera = Helios2Camera()
    
    pcd = camera.get_pointcloud()
    assert pcd is not None
    
    # # ROI box (including conveyor)
    # pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 2000)))
    
    box_position = get_box_position(pcd, visualize=True)
    print(box_position)
